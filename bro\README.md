# AI便利贴 (BRO 3.0)

## 软件概念

AI便利贴是一款创新型浏览器扩展，将传统便利贴的实用性与现代AI技术完美结合。它就像贴在您电脑上的智能便利贴，能够帮助您总结网页内容、回答问题，同时不会打断您的阅读节奏，体现"以静制动"的设计理念。

## 核心功能

1. **智能内容总结**：一键总结当前网页内容，提取关键信息
2. **问答交互**：针对网页内容提问，获取AI助手的回答
3. **多模型支持**：兼容多种AI模型，包括：
   - OpenAI (GPT系列)
   - SiliconFlow (Qwen系列)
   - Google Gemini
   - OpenRouter
   - Ollama (本地模型)
4. **便捷笔记**：自动保存总结和问答内容，支持一键导出
5. **信心指数**：记录使用频率，形成正能量反馈循环

## 使用说明

### 基本操作
1. 点击浏览器工具栏中的扩展图标打开AI便利贴
2. 选择您希望使用的AI平台和模型
3. 选择"总结页面"或"提问"功能
4. 获取AI响应，自动保存为笔记

### 高级设置
- **API设置**：配置各平台API密钥和参数
- **个性化**：调整界面主题、语言和字体大小
- **数据管理**：导出笔记、清除历史记录

### 特色优势

AI便利贴就像一个生命体，它会记录您的使用频率，形成"信心指数"，让您了解自己使用AI辅助阅读和信息提取的习惯。这种方式不仅提高了阅读效率，还能形成一个良性的学习反馈循环，帮助您更加高效地获取和处理信息。

## 与市场同类产品的优劣比较

### 优势

1. **多模型整合**：不同于大多数只支持单一AI服务的扩展，AI便利贴支持多种AI模型（包括本地模型Ollama），用户可根据需求和成本灵活选择。

2. **轻量化设计**：相比于ChatGPT插件、Notion AI等重量级应用，AI便利贴体积小、启动快，不占用过多系统资源，保证流畅的浏览体验。

3. **非侵入式体验**：许多AI阅读工具会替代或改变原始网页内容，而AI便利贴保持原网页完整性，通过弹出界面提供服务，不干扰自然阅读流程。

4. **笔记系统**：与简单的AI问答工具不同，AI便利贴具有内置笔记功能，自动关联页面标题，方便回顾和管理知识。

5. **隐私保护**：支持本地模型，敏感内容无需发送至第三方服务器，更好地保护用户隐私。

6. **信心指数特性**：独特的使用频率统计功能，帮助用户了解自己的AI辅助习惯，形成积极反馈。

### 劣势

1. **功能精简**：相比Notion AI或Claude等全功能AI助手，功能相对集中于阅读辅助，不提供图像生成、代码编写等复杂功能。

2. **依赖第三方API**：除Ollama外，其他模型需要API密钥和网络连接，可能产生费用。

3. **专业领域支持有限**：与垂直领域的专业AI工具相比，在特定专业领域的深度解析能力有待增强。

### 对比竞品

| 特性 | AI便利贴 | ChatGPT扩展 | Notion AI | 传统笔记工具 |
|------|----------|-------------|-----------|--------------|
| 多模型支持 | ✅ | ❌ | ❌ | ❌ |
| 本地模型选项 | ✅ | ❌ | ❌ | ❌ |
| 内容总结 | ✅ | ✅ | ✅ | ❌ |
| 非侵入式体验 | ✅ | ❌ | ❌ | ✅ |
| 自动笔记保存 | ✅ | ❌ | ✅ | ✅ |
| 启动速度 | 快 | 中 | 慢 | 快 |
| 使用统计反馈 | ✅ | ❌ | ❌ | ❌ |
| 无需账户 | ✅ | ❌ | ❌ | ✅/❌ |

AI便利贴定位为"轻量级、非侵入式、多模型支持的阅读辅助工具"，适合日常网页浏览过程中需要快速总结和问答的用户，尤其对注重隐私和希望灵活选择AI模型的用户更具吸引力。

## 技术特点

- 轻量级设计，不影响浏览体验
- 支持多种AI模型，灵活切换
- 本地存储功能，保护隐私
- 响应式界面，适配各种显示设备

## BRO 3.0 更新内容

- 修复了Ollama API的模型名称空格问题
- 更新了模型ID支持
- 优化了用户界面和交互体验
- 增强了多平台兼容性

---

AI便利贴 —— 您的阅读思考助手，让知识获取事半功倍！ 